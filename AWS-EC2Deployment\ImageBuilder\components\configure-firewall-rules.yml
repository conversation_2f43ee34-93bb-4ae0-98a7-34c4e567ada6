# AWS Image Builder Component: Configure Windows Firewall Rules
# This component configures Windows Firewall with security-focused rules for business environments
# Includes Sanlam Shared PRD specific firewall rules

name: win-2022-firewall
description: Configure Windows Firewall rules for business server environments with Sanlam customizations
schemaVersion: 1.0
phases:
- name: build
  steps:
  - name: EnableWindowsFirewall
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring Windows Firewall..."

        # Enable Windows Firewall for all profiles
        Write-Host "Enabling Windows Firewall for all profiles..."
        Set-NetFirewallProfile -All -Enabled True

        # Set default actions
        Write-Host "Setting default firewall actions..."
        Set-NetFirewallProfile -All -DefaultInboundAction Block -DefaultOutboundAction Allow

        # Configure logging
        Write-Host "Configuring firewall logging..."
        Set-NetFirewallProfile -All -LogFileName "%systemroot%\system32\LogFiles\Firewall\pfirewall.log"
        Set-NetFirewallProfile -All -LogMaxSizeKilobytes 4096
        Set-NetFirewallProfile -All -LogBlocked True
        Set-NetFirewallProfile -All -LogAllowed False

  - name: ConfigureBypassRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring bypass firewall rules..."

        # Allow Any Traffic - Any-Any Bypass Rule for Role Based Traffic Rules.
        Write-Host "Allowing All Traffic..."
        New-NetFirewallRule -DisplayName "Allow Any-Any" -Direction Inbound -Action Allow -Protocol Any -Profile Any
        New-NetFirewallRule -DisplayName "Allow Any-Any" -Direction Outbound -Action Allow -Protocol Any -Profile Any

  - name: ConfigureSanlamSpecificRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring Sanlam Shared PRD specific firewall rules..."

        # Allow RDP Inbound - Restricted to internal networks only
        Write-Host "Allowing RDP from internal networks only..."
        New-NetFirewallRule -DisplayName "Allow RDP Inbound" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 3389 -Profile Domain,Private -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Description "Allow RDP from internal networks only"

        # Allow WinRM HTTP - Domain profile only
        Write-Host "Allowing WinRM HTTP for PowerShell remoting..."
        New-NetFirewallRule -DisplayName "Allow WinRM HTTP" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 5985 -Profile Domain -RemoteAddress "10.0.0.0/8" -Description "Allow WinRM for PowerShell remoting"

        # Allow WinRM HTTPS - Domain profile only
        Write-Host "Allowing WinRM HTTPS for secure PowerShell remoting..."
        New-NetFirewallRule -DisplayName "Allow WinRM HTTPS" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 5986 -Profile Domain -RemoteAddress "10.0.0.0/8" -Description "Allow secure WinRM for PowerShell remoting"

        # Allow ICMP Ping - Domain and Private profiles
        Write-Host "Allowing ICMP Ping for network diagnostics..."
        New-NetFirewallRule -DisplayName "Allow ICMP Ping" -Direction Inbound -Action Allow -Protocol ICMPv4 -IcmpType 8 -Profile Domain,Private -Description "Allow ping for network diagnostics"

        # Block Internet Outbound - Security measure
        Write-Host "Blocking direct internet access for security..."
        New-NetFirewallRule -DisplayName "Block Internet Outbound" -Direction Outbound -Action Block -Protocol Any -RemoteAddress "0.0.0.0-*************","********-***************","***********-**************","**********-***************","***********-***************" -Profile Domain,Private,Public -Description "Block direct internet access for security"

        # Allow Internal Network Outbound
        Write-Host "Allowing communication within internal networks..."
        New-NetFirewallRule -DisplayName "Allow Internal Network Outbound" -Direction Outbound -Action Allow -Protocol Any -RemoteAddress "10.0.0.0/8","**********/12","***********/16" -Profile Domain,Private -Description "Allow communication within internal networks"

  - name: ConfigureBasicRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring basic firewall rules..."

        # Allow RDP (Remote Desktop) - Essential for Windows Server management (fallback rule)
        Write-Host "Allowing RDP (Remote Desktop Protocol) - fallback rule..."
        New-NetFirewallRule -DisplayName "Remote Desktop - User Mode (TCP-In)" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 3389 -Profile Any
        New-NetFirewallRule -DisplayName "Remote Desktop - User Mode (UDP-In)" -Direction Inbound -Action Allow -Protocol UDP -LocalPort 3389 -Profile Any

        # Allow WinRM (Windows Remote Management) - For PowerShell remoting (fallback rule)
        Write-Host "Allowing WinRM (Windows Remote Management) - fallback rule..."
        New-NetFirewallRule -DisplayName "Windows Remote Management (HTTP-In)" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 5985 -Profile Any
        New-NetFirewallRule -DisplayName "Windows Remote Management (HTTPS-In)" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 5986 -Profile Any

        # Allow ICMP (Ping) - For network diagnostics (fallback rule)
        Write-Host "Allowing ICMP (Ping) - fallback rule..."
        New-NetFirewallRule -DisplayName "ICMP Allow incoming V4 echo request" -Direction Inbound -Protocol ICMPv4 -IcmpType 8 -Action Allow -Profile Any
        New-NetFirewallRule -DisplayName "ICMP Allow incoming V6 echo request" -Direction Inbound -Protocol ICMPv6 -IcmpType 128 -Action Allow -Profile Any

  - name: ConfigureWebServerRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring web server firewall rules..."

        # Allow HTTP (Port 80)
        Write-Host "Allowing HTTP traffic (port 80)..."
        New-NetFirewallRule -DisplayName "HTTP Inbound" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 80 -Profile Any

        # Allow HTTPS (Port 443)
        Write-Host "Allowing HTTPS traffic (port 443)..."
        New-NetFirewallRule -DisplayName "HTTPS Inbound" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 443 -Profile Any

        # Allow common web application ports
        Write-Host "Allowing common web application ports..."
        New-NetFirewallRule -DisplayName "Web App Port 8080" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 8080 -Profile Any
        New-NetFirewallRule -DisplayName "Web App Port 8443" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 8443 -Profile Any

  - name: ConfigureDatabaseRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring database server firewall rules..."

        # SQL Server default instance (1433)
        Write-Host "Allowing SQL Server default instance (port 1433)..."
        New-NetFirewallRule -DisplayName "SQL Server Database Engine" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 1433 -Profile Any

        # SQL Server Browser Service (1434)
        Write-Host "Allowing SQL Server Browser Service (port 1434)..."
        New-NetFirewallRule -DisplayName "SQL Server Browser Service" -Direction Inbound -Action Allow -Protocol UDP -LocalPort 1434 -Profile Any

        # SQL Server Analysis Services (2383)
        Write-Host "Allowing SQL Server Analysis Services (port 2383)..."
        New-NetFirewallRule -DisplayName "SQL Server Analysis Services" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 2383 -Profile Any

        # SQL Server Reporting Services (80/443 already covered above)
        # SQL Server Integration Services (135 - handled in RPC rules)

  - name: ConfigureActiveDirectoryRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring Active Directory firewall rules..."

        # DNS (53)
        Write-Host "Allowing DNS traffic (port 53)..."
        New-NetFirewallRule -DisplayName "DNS (TCP)" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 53 -Profile Any
        New-NetFirewallRule -DisplayName "DNS (UDP)" -Direction Inbound -Action Allow -Protocol UDP -LocalPort 53 -Profile Any

        # LDAP (389)
        Write-Host "Allowing LDAP traffic (port 389)..."
        New-NetFirewallRule -DisplayName "LDAP" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 389 -Profile Any
        New-NetFirewallRule -DisplayName "LDAP (UDP)" -Direction Inbound -Action Allow -Protocol UDP -LocalPort 389 -Profile Any

        # LDAPS (636)
        Write-Host "Allowing LDAPS traffic (port 636)..."
        New-NetFirewallRule -DisplayName "LDAPS" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 636 -Profile Any

        # Global Catalog (3268, 3269)
        Write-Host "Allowing Global Catalog traffic..."
        New-NetFirewallRule -DisplayName "Global Catalog" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 3268 -Profile Any
        New-NetFirewallRule -DisplayName "Global Catalog SSL" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 3269 -Profile Any

        # Kerberos (88)
        Write-Host "Allowing Kerberos traffic (port 88)..."
        New-NetFirewallRule -DisplayName "Kerberos (TCP)" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 88 -Profile Any
        New-NetFirewallRule -DisplayName "Kerberos (UDP)" -Direction Inbound -Action Allow -Protocol UDP -LocalPort 88 -Profile Any

  - name: ConfigureSystemRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring system service firewall rules..."

        # RPC Endpoint Mapper (135)
        Write-Host "Allowing RPC Endpoint Mapper (port 135)..."
        New-NetFirewallRule -DisplayName "RPC Endpoint Mapper" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 135 -Profile Any

        # NetBIOS (137, 138, 139) - Often needed for legacy applications
        Write-Host "Allowing NetBIOS traffic..."
        New-NetFirewallRule -DisplayName "NetBIOS Name Service" -Direction Inbound -Action Allow -Protocol UDP -LocalPort 137 -Profile Any
        New-NetFirewallRule -DisplayName "NetBIOS Datagram Service" -Direction Inbound -Action Allow -Protocol UDP -LocalPort 138 -Profile Any
        New-NetFirewallRule -DisplayName "NetBIOS Session Service" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 139 -Profile Any

        # SMB/CIFS (445)
        Write-Host "Allowing SMB/CIFS traffic (port 445)..."
        New-NetFirewallRule -DisplayName "SMB/CIFS" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 445 -Profile Any

        # SNMP (161) - For monitoring
        Write-Host "Allowing SNMP traffic (port 161)..."
        New-NetFirewallRule -DisplayName "SNMP" -Direction Inbound -Action Allow -Protocol UDP -LocalPort 161 -Profile Any

  - name: ConfigureSecurityRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring security-focused firewall rules..."

        # Block common attack ports
        Write-Host "Blocking common attack ports..."
        New-NetFirewallRule -DisplayName "Block Telnet" -Direction Inbound -Action Block -Protocol TCP -LocalPort 23 -Profile Any
        New-NetFirewallRule -DisplayName "Block FTP" -Direction Inbound -Action Block -Protocol TCP -LocalPort 21 -Profile Any
        New-NetFirewallRule -DisplayName "Block TFTP" -Direction Inbound -Action Block -Protocol UDP -LocalPort 69 -Profile Any
        New-NetFirewallRule -DisplayName "Block SNMP Trap" -Direction Inbound -Action Block -Protocol UDP -LocalPort 162 -Profile Any

        # Allow Windows Time Service (NTP)
        Write-Host "Allowing NTP traffic (port 123)..."
        New-NetFirewallRule -DisplayName "NTP (UDP)" -Direction Inbound -Action Allow -Protocol UDP -LocalPort 123 -Profile Any
        New-NetFirewallRule -DisplayName "NTP Out (UDP)" -Direction Outbound -Action Allow -Protocol UDP -RemotePort 123 -Profile Any

        # Allow DHCP client (if needed)
        Write-Host "Allowing DHCP client traffic..."
        New-NetFirewallRule -DisplayName "DHCP Client" -Direction Inbound -Action Allow -Protocol UDP -LocalPort 68 -Profile Any
        New-NetFirewallRule -DisplayName "DHCP Client Out" -Direction Outbound -Action Allow -Protocol UDP -RemotePort 67 -Profile Any

  - name: ConfigureOutboundRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring outbound firewall rules..."

        # Allow common outbound ports for updates and management
        Write-Host "Allowing outbound HTTP/HTTPS for updates..."
        New-NetFirewallRule -DisplayName "HTTP Out" -Direction Outbound -Action Allow -Protocol TCP -RemotePort 80 -Profile Any
        New-NetFirewallRule -DisplayName "HTTPS Out" -Direction Outbound -Action Allow -Protocol TCP -RemotePort 443 -Profile Any

        # Allow outbound DNS
        Write-Host "Allowing outbound DNS..."
        New-NetFirewallRule -DisplayName "DNS Out (TCP)" -Direction Outbound -Action Allow -Protocol TCP -RemotePort 53 -Profile Any
        New-NetFirewallRule -DisplayName "DNS Out (UDP)" -Direction Outbound -Action Allow -Protocol UDP -RemotePort 53 -Profile Any

        # Allow outbound LDAP for domain authentication
        Write-Host "Allowing outbound LDAP..."
        New-NetFirewallRule -DisplayName "LDAP Out" -Direction Outbound -Action Allow -Protocol TCP -RemotePort 389 -Profile Any
        New-NetFirewallRule -DisplayName "LDAPS Out" -Direction Outbound -Action Allow -Protocol TCP -RemotePort 636 -Profile Any

        # Allow outbound Kerberos
        Write-Host "Allowing outbound Kerberos..."
        New-NetFirewallRule -DisplayName "Kerberos Out (TCP)" -Direction Outbound -Action Allow -Protocol TCP -RemotePort 88 -Profile Any
        New-NetFirewallRule -DisplayName "Kerberos Out (UDP)" -Direction Outbound -Action Allow -Protocol UDP -RemotePort 88 -Profile Any

- name: validate
  steps:
  - name: ValidateFirewallConfiguration
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Validating Windows Firewall configuration..."

        # Check if firewall is enabled
        $firewallProfiles = Get-NetFirewallProfile
        $disabledProfiles = $firewallProfiles | Where-Object { $_.Enabled -eq $false }
        if ($disabledProfiles.Count -gt 0) {
            Write-Error "VALIDATION FAILED: Windows Firewall is not enabled on profiles: $($disabledProfiles.Name -join ', ')"
            exit 1
        }

        # Check if key rules exist
        $existingRules = Get-NetFirewallRule | Where-Object { $_.Enabled -eq "True" }

        $requiredRules = @(
            "Remote Desktop - User Mode",
            "Windows Remote Management",
            "HTTP Inbound",
            "HTTPS Inbound",
            "DNS (TCP)",
            "LDAP"
        )

        $missingRules = @()
        foreach ($rule in $requiredRules) {
            $foundRule = $existingRules | Where-Object { $_.DisplayName -like "*$rule*" }
            if (-not $foundRule) {
                $missingRules += $rule
            }
        }

        if ($missingRules.Count -eq 0) {
            Write-Host "VALIDATION SUCCESS: All required firewall rules are configured"

            # Display summary
            Write-Host "`nFirewall Configuration Summary:"
            Write-Host "- Windows Firewall: Enabled on all profiles"
            Write-Host "- Default Policy: Block inbound, Allow outbound"
            Write-Host "- Logging: Enabled for dropped connections"
            Write-Host "- Key services: RDP, WinRM, HTTP/HTTPS, DNS, LDAP configured"
            Write-Host "- Total active rules: $($existingRules.Count)"

            exit 0
        } else {
            Write-Error "VALIDATION FAILED: Missing firewall rules: $($missingRules -join ', ')"
            exit 1
        }
