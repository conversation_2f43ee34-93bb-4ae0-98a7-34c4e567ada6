# AWS Image Builder Component: Configure Windows Registry Settings
# This component configures Windows Registry settings and optimizations for business environments

name: configure-registry-settings
description: Configure Windows Registry settings, optimizations, and security configurations
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: ConfigureWindowsUpdateSettings
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Configuring Windows Update registry settings..."

                # Create Windows Update registry paths if they don't exist
                $auPath = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU"
                $wuPath = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate"

                if (-not (Test-Path $auPath)) {
                    New-Item -Path $auPath -Force | Out-Null
                    Write-Host "Created registry path: $auPath"
                }

                if (-not (Test-Path $wuPath)) {
                    New-Item -Path $wuPath -Force | Out-Null
                    Write-Host "Created registry path: $wuPath"
                }

                # Enable automatic updates
                Write-Host "Enabling automatic updates..."
                Set-ItemProperty -Path $auPath -Name "NoAutoUpdate" -Value 0 -Type DWord

                # Auto download and schedule install
                Write-Host "Setting auto download and schedule install..."
                Set-ItemProperty -Path $auPath -Name "AUOptions" -Value 4 -Type DWord

                # Configure internal WSUS server
                Write-Host "Configuring internal WSUS server..."
                Set-ItemProperty -Path $wuPath -Name "WUServer" -Value "http://wsus.sanlam.co.za:8530" -Type String
                Set-ItemProperty -Path $wuPath -Name "WUStatusServer" -Value "http://wsus.sanlam.co.za:8530" -Type String

                # Use WSUS server for updates
                Set-ItemProperty -Path $wuPath -Name "UseWUServer" -Value 1 -Type DWord

      - name: ConfigureRemoteDesktopSettings
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Configuring Remote Desktop registry settings..."

                # Enable Remote Desktop
                Write-Host "Enabling Remote Desktop..."
                $rdpPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server"
                Set-ItemProperty -Path $rdpPath -Name "fDenyTSConnections" -Value 0 -Type DWord

      - name: ConfigureSecuritySettings
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Configuring security registry settings..."

                # Enable User Account Control with enhanced settings
                Write-Host "Enabling User Account Control..."
                $uacPath = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System"
                Set-ItemProperty -Path $uacPath -Name "EnableLUA" -Value 1 -Type DWord
                Set-ItemProperty -Path $uacPath -Name "ConsentPromptBehaviorAdmin" -Value 2 -Type DWord

                # Disable AutoRun for all drives for security
                Write-Host "Disabling AutoRun for security..."
                $explorerPath = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\Explorer"
                if (-not (Test-Path $explorerPath)) {
                    New-Item -Path $explorerPath -Force | Out-Null
                }
                Set-ItemProperty -Path $explorerPath -Name "NoDriveTypeAutoRun" -Value 255 -Type DWord

                # Configure Windows Defender settings
                Write-Host "Configuring Windows Defender..."
                $defenderPath = "HKLM:\SOFTWARE\Microsoft\Windows Defender\Real-Time Protection"
                if (Test-Path $defenderPath) {
                    Set-ItemProperty -Path $defenderPath -Name "DisableRealtimeMonitoring" -Value 0 -Type DWord -ErrorAction SilentlyContinue
                }

      - name: ConfigureEventLogSettings
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Configuring Event Log registry settings..."

                # Create Event Log policy paths if they don't exist
                $appLogPath = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\EventLog\Application"
                $secLogPath = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\EventLog\Security"
                $sysLogPath = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\EventLog\System"

                foreach ($path in @($appLogPath, $secLogPath, $sysLogPath)) {
                    if (-not (Test-Path $path)) {
                        New-Item -Path $path -Force | Out-Null
                        Write-Host "Created registry path: $path"
                    }
                }

                # Set Application log size to 32MB
                Write-Host "Setting Application log size to 32MB..."
                Set-ItemProperty -Path $appLogPath -Name "MaxSize" -Value 32768 -Type DWord

                # Set Security log size to 128MB
                Write-Host "Setting Security log size to 128MB..."
                Set-ItemProperty -Path $secLogPath -Name "MaxSize" -Value 131072 -Type DWord

                # Set System log size to 32MB
                Write-Host "Setting System log size to 32MB..."
                Set-ItemProperty -Path $sysLogPath -Name "MaxSize" -Value 32768 -Type DWord

      - name: ConfigurePerformanceOptimizations
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Applying Windows performance optimizations..."

                # Disable Windows Error Reporting
                Write-Host "Disabling Windows Error Reporting..."
                $werPath = "HKLM:\SOFTWARE\Microsoft\Windows\Windows Error Reporting"
                if (-not (Test-Path $werPath)) {
                    New-Item -Path $werPath -Force | Out-Null
                }
                Set-ItemProperty -Path $werPath -Name "Disabled" -Value 1 -Type DWord

                # Disable Customer Experience Improvement Program
                Write-Host "Disabling Customer Experience Improvement Program..."
                $ceipPath = "HKLM:\SOFTWARE\Microsoft\SQMClient\Windows"
                if (-not (Test-Path $ceipPath)) {
                    New-Item -Path $ceipPath -Force | Out-Null
                }
                Set-ItemProperty -Path $ceipPath -Name "CEIPEnable" -Value 0 -Type DWord

                # Optimize system settings
                Write-Host "Optimizing system settings..."
                Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control" -Name "WaitToKillServiceTimeout" -Value "2000" -Type String
                Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control" -Name "ServicesPipeTimeout" -Value 60000 -Type DWord

  - name: validate
    steps:
      - name: ValidateRegistryConfiguration
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Validating Windows Registry configuration..."

                $validationErrors = @()

                # Validate Windows Update settings
                try {
                    $noAutoUpdate = Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU" -Name "NoAutoUpdate" | Select-Object -ExpandProperty NoAutoUpdate
                    if ($noAutoUpdate -ne 0) {
                        $validationErrors += "NoAutoUpdate should be 0 (enabled), found: $noAutoUpdate"
                    }

                    $auOptions = Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU" -Name "AUOptions" | Select-Object -ExpandProperty AUOptions
                    if ($auOptions -ne 4) {
                        $validationErrors += "AUOptions should be 4 (auto download and schedule), found: $auOptions"
                    }

                    $wuServer = Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate" -Name "WUServer" | Select-Object -ExpandProperty WUServer
                    if ($wuServer -ne "http://wsus.sanlam.co.za:8530") {
                        $validationErrors += "WUServer should be 'http://wsus.sanlam.co.za:8530', found: $wuServer"
                    }
                }
                catch {
                    $validationErrors += "Failed to validate Windows Update settings: $($_.Exception.Message)"
                }

                # Validate Remote Desktop settings
                try {
                    $rdpEnabled = Get-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server" -Name "fDenyTSConnections" | Select-Object -ExpandProperty fDenyTSConnections
                    if ($rdpEnabled -ne 0) {
                        $validationErrors += "Remote Desktop should be enabled (0), found: $rdpEnabled"
                    }
                }
                catch {
                    $validationErrors += "Failed to validate Remote Desktop settings: $($_.Exception.Message)"
                }

                # Validate UAC settings
                try {
                    $uacEnabled = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" -Name "EnableLUA" | Select-Object -ExpandProperty EnableLUA
                    if ($uacEnabled -ne 1) {
                        $validationErrors += "UAC should be enabled (1), found: $uacEnabled"
                    }
                }
                catch {
                    $validationErrors += "Failed to validate UAC settings: $($_.Exception.Message)"
                }

                # Validate Event Log settings
                try {
                    $appLogSize = Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\EventLog\Application" -Name "MaxSize" | Select-Object -ExpandProperty MaxSize
                    if ($appLogSize -ne 32768) {
                        $validationErrors += "Application log size should be 32768, found: $appLogSize"
                    }

                    $secLogSize = Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\EventLog\Security" -Name "MaxSize" | Select-Object -ExpandProperty MaxSize
                    if ($secLogSize -ne 131072) {
                        $validationErrors += "Security log size should be 131072, found: $secLogSize"
                    }
                }
                catch {
                    $validationErrors += "Failed to validate Event Log settings: $($_.Exception.Message)"
                }

                # Validate performance optimization settings
                try {
                    $werDisabled = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\Windows Error Reporting" -Name "Disabled" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty Disabled
                    if ($werDisabled -ne 1) {
                        $validationErrors += "Windows Error Reporting should be disabled (1), found: $werDisabled"
                    }

                    $ceipDisabled = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\SQMClient\Windows" -Name "CEIPEnable" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty CEIPEnable
                    if ($ceipDisabled -ne 0) {
                        $validationErrors += "Customer Experience Improvement Program should be disabled (0), found: $ceipDisabled"
                    }

                    $autoRunDisabled = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\Explorer" -Name "NoDriveTypeAutoRun" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty NoDriveTypeAutoRun
                    if ($autoRunDisabled -ne 255) {
                        $validationErrors += "AutoRun should be disabled (255), found: $autoRunDisabled"
                    }
                }
                catch {
                    $validationErrors += "Failed to validate performance optimization settings: $($_.Exception.Message)"
                }

                # Report validation results
                if ($validationErrors.Count -eq 0) {
                    Write-Host "VALIDATION SUCCESS: All registry settings are configured correctly"
                    
                    # Display summary
                    Write-Host "`nRegistry Configuration Summary:"
                    Write-Host "- Windows Update: Configured for internal WSUS server"
                    Write-Host "- Remote Desktop: Enabled"
                    Write-Host "- User Account Control: Enabled with enhanced settings"
                    Write-Host "- Security: AutoRun disabled, Windows Defender configured"
                    Write-Host "- Event Logs: Application (32MB), Security (128MB), System (32MB)"
                    Write-Host "- Performance: Windows Error Reporting and CEIP disabled"
                    
                    exit 0
                } else {
                    Write-Error "VALIDATION FAILED: Registry configuration errors found:"
                    foreach ($error in $validationErrors) {
                        Write-Error "  - $error"
                    }
                    exit 1
                }
