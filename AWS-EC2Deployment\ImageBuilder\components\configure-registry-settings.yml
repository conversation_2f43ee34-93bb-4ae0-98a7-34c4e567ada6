# AWS Image Builder Component: Configure Windows Registry Settings
# This component configures Windows Registry settings for Sanlam Shared PRD environment

name: sanlam-registry-settings
description: Configure Windows Registry settings for Sanlam Shared PRD environment
schemaVersion: 1.0
phases:
- name: build
  steps:
  - name: ConfigureWindowsUpdateSettings
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring Windows Update registry settings..."

        # Create Windows Update registry paths if they don't exist
        $auPath = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU"
        $wuPath = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate"
        
        if (-not (Test-Path $auPath)) {
            New-Item -Path $auPath -Force | Out-Null
            Write-Host "Created registry path: $auPath"
        }
        
        if (-not (Test-Path $wuPath)) {
            New-Item -Path $wuPath -Force | Out-Null
            Write-Host "Created registry path: $wuPath"
        }

        # Enable automatic updates
        Write-Host "Enabling automatic updates..."
        Set-ItemProperty -Path $auPath -Name "NoAutoUpdate" -Value 0 -Type DWord

        # Auto download and schedule install
        Write-Host "Setting auto download and schedule install..."
        Set-ItemProperty -Path $auPath -Name "AUOptions" -Value 4 -Type DWord

        # Configure internal WSUS server
        Write-Host "Configuring internal WSUS server..."
        Set-ItemProperty -Path $wuPath -Name "WUServer" -Value "http://wsus.sanlam.co.za:8530" -Type String
        Set-ItemProperty -Path $wuPath -Name "WUStatusServer" -Value "http://wsus.sanlam.co.za:8530" -Type String

  - name: ConfigureRemoteDesktopSettings
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring Remote Desktop registry settings..."

        # Enable Remote Desktop
        Write-Host "Enabling Remote Desktop..."
        $rdpPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server"
        Set-ItemProperty -Path $rdpPath -Name "fDenyTSConnections" -Value 0 -Type DWord

  - name: ConfigureSecuritySettings
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring security registry settings..."

        # Enable User Account Control
        Write-Host "Enabling User Account Control..."
        $uacPath = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System"
        Set-ItemProperty -Path $uacPath -Name "EnableLUA" -Value 1 -Type DWord

  - name: ConfigureEventLogSettings
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring Event Log registry settings..."

        # Create Event Log policy paths if they don't exist
        $appLogPath = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\EventLog\Application"
        $secLogPath = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\EventLog\Security"
        $sysLogPath = "HKLM:\SOFTWARE\Policies\Microsoft\Windows\EventLog\System"
        
        foreach ($path in @($appLogPath, $secLogPath, $sysLogPath)) {
            if (-not (Test-Path $path)) {
                New-Item -Path $path -Force | Out-Null
                Write-Host "Created registry path: $path"
            }
        }

        # Set Application log size to 32MB
        Write-Host "Setting Application log size to 32MB..."
        Set-ItemProperty -Path $appLogPath -Name "MaxSize" -Value 32768 -Type DWord

        # Set Security log size to 128MB
        Write-Host "Setting Security log size to 128MB..."
        Set-ItemProperty -Path $secLogPath -Name "MaxSize" -Value 131072 -Type DWord

        # Set System log size to 32MB
        Write-Host "Setting System log size to 32MB..."
        Set-ItemProperty -Path $sysLogPath -Name "MaxSize" -Value 32768 -Type DWord

  - name: ConfigureSanlamDeploymentSettings
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring Sanlam deployment registry settings..."

        # Create Sanlam deployment registry path
        $sanlamPath = "HKLM:\SOFTWARE\Sanlam\Deployment"
        if (-not (Test-Path $sanlamPath)) {
            New-Item -Path $sanlamPath -Force | Out-Null
            Write-Host "Created registry path: $sanlamPath"
        }

        # Mark server environment for identification
        Write-Host "Setting environment identifier..."
        Set-ItemProperty -Path $sanlamPath -Name "Environment" -Value "DEV" -Type String

        # Record deployment date
        Write-Host "Recording deployment date..."
        $deploymentDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        Set-ItemProperty -Path $sanlamPath -Name "DeploymentDate" -Value $deploymentDate -Type String

- name: validate
  steps:
  - name: ValidateRegistryConfiguration
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Validating Windows Registry configuration..."

        $validationErrors = @()

        # Validate Windows Update settings
        try {
            $noAutoUpdate = Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU" -Name "NoAutoUpdate" | Select-Object -ExpandProperty NoAutoUpdate
            if ($noAutoUpdate -ne 0) {
                $validationErrors += "NoAutoUpdate should be 0 (enabled), found: $noAutoUpdate"
            }

            $auOptions = Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU" -Name "AUOptions" | Select-Object -ExpandProperty AUOptions
            if ($auOptions -ne 4) {
                $validationErrors += "AUOptions should be 4 (auto download and schedule), found: $auOptions"
            }

            $wuServer = Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate" -Name "WUServer" | Select-Object -ExpandProperty WUServer
            if ($wuServer -ne "http://wsus.sanlam.co.za:8530") {
                $validationErrors += "WUServer should be 'http://wsus.sanlam.co.za:8530', found: $wuServer"
            }
        }
        catch {
            $validationErrors += "Failed to validate Windows Update settings: $($_.Exception.Message)"
        }

        # Validate Remote Desktop settings
        try {
            $rdpEnabled = Get-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Terminal Server" -Name "fDenyTSConnections" | Select-Object -ExpandProperty fDenyTSConnections
            if ($rdpEnabled -ne 0) {
                $validationErrors += "Remote Desktop should be enabled (0), found: $rdpEnabled"
            }
        }
        catch {
            $validationErrors += "Failed to validate Remote Desktop settings: $($_.Exception.Message)"
        }

        # Validate UAC settings
        try {
            $uacEnabled = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" -Name "EnableLUA" | Select-Object -ExpandProperty EnableLUA
            if ($uacEnabled -ne 1) {
                $validationErrors += "UAC should be enabled (1), found: $uacEnabled"
            }
        }
        catch {
            $validationErrors += "Failed to validate UAC settings: $($_.Exception.Message)"
        }

        # Validate Event Log settings
        try {
            $appLogSize = Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\EventLog\Application" -Name "MaxSize" | Select-Object -ExpandProperty MaxSize
            if ($appLogSize -ne 32768) {
                $validationErrors += "Application log size should be 32768, found: $appLogSize"
            }

            $secLogSize = Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\EventLog\Security" -Name "MaxSize" | Select-Object -ExpandProperty MaxSize
            if ($secLogSize -ne 131072) {
                $validationErrors += "Security log size should be 131072, found: $secLogSize"
            }
        }
        catch {
            $validationErrors += "Failed to validate Event Log settings: $($_.Exception.Message)"
        }

        # Validate Sanlam deployment settings
        try {
            $environment = Get-ItemProperty -Path "HKLM:\SOFTWARE\Sanlam\Deployment" -Name "Environment" | Select-Object -ExpandProperty Environment
            if ($environment -ne "DEV") {
                $validationErrors += "Environment should be 'DEV', found: $environment"
            }

            $deploymentDate = Get-ItemProperty -Path "HKLM:\SOFTWARE\Sanlam\Deployment" -Name "DeploymentDate" | Select-Object -ExpandProperty DeploymentDate
            if (-not $deploymentDate) {
                $validationErrors += "DeploymentDate should be set"
            }
        }
        catch {
            $validationErrors += "Failed to validate Sanlam deployment settings: $($_.Exception.Message)"
        }

        # Report validation results
        if ($validationErrors.Count -eq 0) {
            Write-Host "VALIDATION SUCCESS: All registry settings are configured correctly"
            
            # Display summary
            Write-Host "`nRegistry Configuration Summary:"
            Write-Host "- Windows Update: Configured for internal WSUS server"
            Write-Host "- Remote Desktop: Enabled"
            Write-Host "- User Account Control: Enabled"
            Write-Host "- Event Logs: Application (32MB), Security (128MB), System (32MB)"
            Write-Host "- Sanlam Settings: Environment and deployment date configured"
            
            exit 0
        } else {
            Write-Error "VALIDATION FAILED: Registry configuration errors found:"
            foreach ($error in $validationErrors) {
                Write-Error "  - $error"
            }
            exit 1
        }
