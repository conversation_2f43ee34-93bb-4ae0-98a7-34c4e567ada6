# AWS Image Builder Component: Configure Windows Firewall Rules
# This component configures Windows Firewall with security-focused rules for business environments

name: win-2022-firewall
description: Configure Windows Firewall rules for business server environments
schemaVersion: 1.0
phases:
- name: build
  steps:
  - name: EnableWindowsFirewall
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring Windows Firewall..."

        # Enable Windows Firewall for all profiles
        Write-Host "Enabling Windows Firewall for all profiles..."
        netsh advfirewall set allprofiles state on

        # Set default actions
        Write-Host "Setting default firewall actions..."
        netsh advfirewall set allprofiles firewallpolicy blockinbound,allowoutbound

        # Configure logging
        Write-Host "Configuring firewall logging..."
        netsh advfirewall set allprofiles logging filename %systemroot%\system32\LogFiles\Firewall\pfirewall.log
        netsh advfirewall set allprofiles logging maxfilesize 4096
        netsh advfirewall set allprofiles logging droppedconnections enable
        netsh advfirewall set allprofiles logging allowedconnections disable

  - name: ConfigureBypassRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring bypass firewall rules..."

        # Allow Any Traffic - Any-Any Bypass Rule for Role Based Traffic Rules.
        Write-Host "Allowing All Traffic..."
        New-NetFirewallRule -DisplayName "Allow Any-Any" -Direction Inbound -Action Allow -Protocol Any -Profile Any
        New-NetFirewallRule -DisplayName "Allow Any-Any" -Direction Outbound -Action Allow -Protocol Any -Profile Any

  - name: ConfigureBasicRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring basic firewall rules..."

        # Allow RDP (Remote Desktop) - Essential for Windows Server management
        Write-Host "Allowing RDP (Remote Desktop Protocol)..."
        netsh advfirewall firewall add rule name="Remote Desktop - User Mode (TCP-In)" dir=in action=allow protocol=TCP localport=3389 profile=any
        netsh advfirewall firewall add rule name="Remote Desktop - User Mode (UDP-In)" dir=in action=allow protocol=UDP localport=3389 profile=any

        # Allow WinRM (Windows Remote Management) - For PowerShell remoting
        Write-Host "Allowing WinRM (Windows Remote Management)..."
        netsh advfirewall firewall add rule name="Windows Remote Management (HTTP-In)" dir=in action=allow protocol=TCP localport=5985 profile=any
        netsh advfirewall firewall add rule name="Windows Remote Management (HTTPS-In)" dir=in action=allow protocol=TCP localport=5986 profile=any

        # Allow ICMP (Ping) - For network diagnostics
        Write-Host "Allowing ICMP (Ping)..."
        netsh advfirewall firewall add rule name="ICMP Allow incoming V4 echo request" dir=in protocol=icmpv4:8,any action=allow profile=any
        netsh advfirewall firewall add rule name="ICMP Allow incoming V6 echo request" dir=in protocol=icmpv6:128,any action=allow profile=any

  - name: ConfigureWebServerRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring web server firewall rules..."

        # Allow HTTP (Port 80)
        Write-Host "Allowing HTTP traffic (port 80)..."
        netsh advfirewall firewall add rule name="HTTP Inbound" dir=in action=allow protocol=TCP localport=80 profile=any

        # Allow HTTPS (Port 443)
        Write-Host "Allowing HTTPS traffic (port 443)..."
        netsh advfirewall firewall add rule name="HTTPS Inbound" dir=in action=allow protocol=TCP localport=443 profile=any

        # Allow common web application ports
        Write-Host "Allowing common web application ports..."
        netsh advfirewall firewall add rule name="Web App Port 8080" dir=in action=allow protocol=TCP localport=8080 profile=any
        netsh advfirewall firewall add rule name="Web App Port 8443" dir=in action=allow protocol=TCP localport=8443 profile=any

  - name: ConfigureDatabaseRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring database server firewall rules..."

        # SQL Server default instance (1433)
        Write-Host "Allowing SQL Server default instance (port 1433)..."
        netsh advfirewall firewall add rule name="SQL Server Database Engine" dir=in action=allow protocol=TCP localport=1433 profile=any

        # SQL Server Browser Service (1434)
        Write-Host "Allowing SQL Server Browser Service (port 1434)..."
        netsh advfirewall firewall add rule name="SQL Server Browser Service" dir=in action=allow protocol=UDP localport=1434 profile=any

        # SQL Server Analysis Services (2383)
        Write-Host "Allowing SQL Server Analysis Services (port 2383)..."
        netsh advfirewall firewall add rule name="SQL Server Analysis Services" dir=in action=allow protocol=TCP localport=2383 profile=any

        # SQL Server Reporting Services (80/443 already covered above)
        # SQL Server Integration Services (135 - handled in RPC rules)

  - name: ConfigureActiveDirectoryRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring Active Directory firewall rules..."

        # DNS (53)
        Write-Host "Allowing DNS traffic (port 53)..."
        netsh advfirewall firewall add rule name="DNS (TCP)" dir=in action=allow protocol=TCP localport=53 profile=any
        netsh advfirewall firewall add rule name="DNS (UDP)" dir=in action=allow protocol=UDP localport=53 profile=any

        # LDAP (389)
        Write-Host "Allowing LDAP traffic (port 389)..."
        netsh advfirewall firewall add rule name="LDAP" dir=in action=allow protocol=TCP localport=389 profile=any
        netsh advfirewall firewall add rule name="LDAP (UDP)" dir=in action=allow protocol=UDP localport=389 profile=any

        # LDAPS (636)
        Write-Host "Allowing LDAPS traffic (port 636)..."
        netsh advfirewall firewall add rule name="LDAPS" dir=in action=allow protocol=TCP localport=636 profile=any

        # Global Catalog (3268, 3269)
        Write-Host "Allowing Global Catalog traffic..."
        netsh advfirewall firewall add rule name="Global Catalog" dir=in action=allow protocol=TCP localport=3268 profile=any
        netsh advfirewall firewall add rule name="Global Catalog SSL" dir=in action=allow protocol=TCP localport=3269 profile=any

        # Kerberos (88)
        Write-Host "Allowing Kerberos traffic (port 88)..."
        netsh advfirewall firewall add rule name="Kerberos (TCP)" dir=in action=allow protocol=TCP localport=88 profile=any
        netsh advfirewall firewall add rule name="Kerberos (UDP)" dir=in action=allow protocol=UDP localport=88 profile=any

  - name: ConfigureSystemRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring system service firewall rules..."

        # RPC Endpoint Mapper (135)
        Write-Host "Allowing RPC Endpoint Mapper (port 135)..."
        netsh advfirewall firewall add rule name="RPC Endpoint Mapper" dir=in action=allow protocol=TCP localport=135 profile=any

        # NetBIOS (137, 138, 139) - Often needed for legacy applications
        Write-Host "Allowing NetBIOS traffic..."
        netsh advfirewall firewall add rule name="NetBIOS Name Service" dir=in action=allow protocol=UDP localport=137 profile=any
        netsh advfirewall firewall add rule name="NetBIOS Datagram Service" dir=in action=allow protocol=UDP localport=138 profile=any
        netsh advfirewall firewall add rule name="NetBIOS Session Service" dir=in action=allow protocol=TCP localport=139 profile=any

        # SMB/CIFS (445)
        Write-Host "Allowing SMB/CIFS traffic (port 445)..."
        netsh advfirewall firewall add rule name="SMB/CIFS" dir=in action=allow protocol=TCP localport=445 profile=any

        # SNMP (161) - For monitoring
        Write-Host "Allowing SNMP traffic (port 161)..."
        netsh advfirewall firewall add rule name="SNMP" dir=in action=allow protocol=UDP localport=161 profile=any

  - name: ConfigureSecurityRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring security-focused firewall rules..."

        # Block common attack ports
        Write-Host "Blocking common attack ports..."
        netsh advfirewall firewall add rule name="Block Telnet" dir=in action=block protocol=TCP localport=23 profile=any
        netsh advfirewall firewall add rule name="Block FTP" dir=in action=block protocol=TCP localport=21 profile=any
        netsh advfirewall firewall add rule name="Block TFTP" dir=in action=block protocol=UDP localport=69 profile=any
        netsh advfirewall firewall add rule name="Block SNMP Trap" dir=in action=block protocol=UDP localport=162 profile=any

        # Allow Windows Time Service (NTP)
        Write-Host "Allowing NTP traffic (port 123)..."
        netsh advfirewall firewall add rule name="NTP (UDP)" dir=in action=allow protocol=UDP localport=123 profile=any
        netsh advfirewall firewall add rule name="NTP Out (UDP)" dir=out action=allow protocol=UDP remoteport=123 profile=any

        # Allow DHCP client (if needed)
        Write-Host "Allowing DHCP client traffic..."
        netsh advfirewall firewall add rule name="DHCP Client" dir=in action=allow protocol=UDP localport=68 profile=any
        netsh advfirewall firewall add rule name="DHCP Client Out" dir=out action=allow protocol=UDP remoteport=67 profile=any

  - name: ConfigureOutboundRules
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Configuring outbound firewall rules..."

        # Allow common outbound ports for updates and management
        Write-Host "Allowing outbound HTTP/HTTPS for updates..."
        netsh advfirewall firewall add rule name="HTTP Out" dir=out action=allow protocol=TCP remoteport=80 profile=any
        netsh advfirewall firewall add rule name="HTTPS Out" dir=out action=allow protocol=TCP remoteport=443 profile=any

        # Allow outbound DNS
        Write-Host "Allowing outbound DNS..."
        netsh advfirewall firewall add rule name="DNS Out (TCP)" dir=out action=allow protocol=TCP remoteport=53 profile=any
        netsh advfirewall firewall add rule name="DNS Out (UDP)" dir=out action=allow protocol=UDP remoteport=53 profile=any

        # Allow outbound LDAP for domain authentication
        Write-Host "Allowing outbound LDAP..."
        netsh advfirewall firewall add rule name="LDAP Out" dir=out action=allow protocol=TCP remoteport=389 profile=any
        netsh advfirewall firewall add rule name="LDAPS Out" dir=out action=allow protocol=TCP remoteport=636 profile=any

        # Allow outbound Kerberos
        Write-Host "Allowing outbound Kerberos..."
        netsh advfirewall firewall add rule name="Kerberos Out (TCP)" dir=out action=allow protocol=TCP remoteport=88 profile=any
        netsh advfirewall firewall add rule name="Kerberos Out (UDP)" dir=out action=allow protocol=UDP remoteport=88 profile=any

- name: validate
  steps:
  - name: ValidateFirewallConfiguration
    action: ExecutePowerShell
    inputs:
      commands:
      - |
        Write-Host "Validating Windows Firewall configuration..."

        # Check if firewall is enabled
        $firewallStatus = netsh advfirewall show allprofiles state
        if ($firewallStatus -notmatch "State\s+ON") {
            Write-Error "VALIDATION FAILED: Windows Firewall is not enabled on all profiles"
            exit 1
        }

        # Check if key rules exist
        $rules = netsh advfirewall firewall show rule name=all | Out-String

        $requiredRules = @(
            "Remote Desktop - User Mode",
            "Windows Remote Management",
            "HTTP Inbound",
            "HTTPS Inbound",
            "DNS \(TCP\)",
            "LDAP"
        )

        $missingRules = @()
        foreach ($rule in $requiredRules) {
            if ($rules -notmatch $rule) {
                $missingRules += $rule
            }
        }

        if ($missingRules.Count -eq 0) {
            Write-Host "VALIDATION SUCCESS: All required firewall rules are configured"
            
            # Display summary
            Write-Host "`nFirewall Configuration Summary:"
            Write-Host "- Windows Firewall: Enabled on all profiles"
            Write-Host "- Default Policy: Block inbound, Allow outbound"
            Write-Host "- Logging: Enabled for dropped connections"
            Write-Host "- Key services: RDP, WinRM, HTTP/HTTPS, DNS, LDAP configured"
            
            exit 0
        } else {
            Write-Error "VALIDATION FAILED: Missing firewall rules: $($missingRules -join ', ')"
            exit 1
        }
